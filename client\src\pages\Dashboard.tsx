import React from 'react';

const stats = [
  { name: 'Commandes totales', value: '12', change: '+4.75%', changeType: 'positive' },
  { name: 'Commandes en cours', value: '8', change: '+54.02%', changeType: 'positive' },
  { name: 'Valeur totale', value: '€2.4M', change: '-1.39%', changeType: 'negative' },
  { name: '<PERSON><PERSON><PERSON>', value: '24.5j', change: '+10.18%', changeType: 'negative' },
];

const recentOrders = [
  { id: 'ORD-001', supplier: 'Fournisseur A', status: 'En transit', amount: '€45,000', date: '2024-01-15' },
  { id: 'ORD-002', supplier: 'Fournisseur B', status: 'Livré', amount: '€32,000', date: '2024-01-14' },
  { id: 'ORD-003', supplier: 'Fournisseur C', status: 'En douane', amount: '€78,000', date: '2024-01-13' },
  { id: 'ORD-004', supplier: 'Fournisseur D', status: 'Commandé', amount: '€25,000', date: '2024-01-12' },
];

export default function Dashboard() {
  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-semibold text-gray-900">Tableau de bord</h1>
        <p className="mt-2 text-sm text-gray-700">
          Vue d'ensemble de votre système de gestion logistique
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {stats.map((item) => (
          <div key={item.name} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">{item.name}</dt>
                    <dd>
                      <div className="text-lg font-medium text-gray-900">{item.value}</div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 px-5 py-3">
              <div className="text-sm">
                <span className={`font-medium ${item.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}`}>
                  {item.change}
                </span>
                <span className="text-gray-500"> depuis le mois dernier</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Orders */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Commandes récentes
            </h3>
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {recentOrders.map((order) => (
                  <li key={order.id} className="py-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-700">
                            {order.id.split('-')[1]}
                          </span>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {order.id}
                        </p>
                        <p className="text-sm text-gray-500 truncate">
                          {order.supplier}
                        </p>
                      </div>
                      <div className="flex-shrink-0 text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {order.amount}
                        </p>
                        <p className="text-sm text-gray-500">
                          {order.date}
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          order.status === 'Livré' ? 'bg-green-100 text-green-800' :
                          order.status === 'En transit' ? 'bg-blue-100 text-blue-800' :
                          order.status === 'En douane' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {order.status}
                        </span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
            <div className="mt-6">
              <a href="/orders" className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Voir toutes les commandes
              </a>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Actions rapides
            </h3>
            <div className="grid grid-cols-1 gap-4">
              <button className="flex items-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                    <span className="text-lg">📋</span>
                  </div>
                </div>
                <div className="ml-3 text-left">
                  <p className="text-sm font-medium text-gray-900">Nouvelle commande</p>
                  <p className="text-sm text-gray-500">Créer une nouvelle commande</p>
                </div>
              </button>
              
              <button className="flex items-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <span className="text-lg">📦</span>
                  </div>
                </div>
                <div className="ml-3 text-left">
                  <p className="text-sm font-medium text-gray-900">Nouvelle réception</p>
                  <p className="text-sm text-gray-500">Enregistrer une réception</p>
                </div>
              </button>
              
              <button className="flex items-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <span className="text-lg">💰</span>
                  </div>
                </div>
                <div className="ml-3 text-left">
                  <p className="text-sm font-medium text-gray-900">Ajouter des coûts</p>
                  <p className="text-sm text-gray-500">Enregistrer des frais</p>
                </div>
              </button>
              
              <button className="flex items-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-lg">📊</span>
                  </div>
                </div>
                <div className="ml-3 text-left">
                  <p className="text-sm font-medium text-gray-900">Générer rapport</p>
                  <p className="text-sm text-gray-500">Exporter les données</p>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
