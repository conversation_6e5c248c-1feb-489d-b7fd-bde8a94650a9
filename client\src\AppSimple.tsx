import React from 'react';

function AppSimple() {
  return (
    <div style={{ 
      minHeight: '100vh', 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center',
      backgroundColor: '#f3f4f6',
      fontFamily: 'system-ui, sans-serif'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '0.5rem',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        textAlign: 'center',
        maxWidth: '400px'
      }}>
        <h1 style={{ 
          fontSize: '1.5rem', 
          fontWeight: 'bold', 
          marginBottom: '1rem',
          color: '#111827'
        }}>
          🚀 Système de Gestion Logistique
        </h1>
        <p style={{ 
          color: '#6b7280', 
          marginBottom: '1.5rem' 
        }}>
          Application en cours de chargement...
        </p>
        <div style={{
          display: 'grid',
          gap: '0.5rem'
        }}>
          <div style={{ 
            padding: '0.5rem', 
            backgroundColor: '#f9fafb', 
            borderRadius: '0.25rem',
            fontSize: '0.875rem'
          }}>
            ✅ Backend: Port 3001
          </div>
          <div style={{ 
            padding: '0.5rem', 
            backgroundColor: '#f9fafb', 
            borderRadius: '0.25rem',
            fontSize: '0.875rem'
          }}>
            ✅ Frontend: Port 5173
          </div>
          <div style={{ 
            padding: '0.5rem', 
            backgroundColor: '#dbeafe', 
            borderRadius: '0.25rem',
            fontSize: '0.875rem',
            color: '#1d4ed8'
          }}>
            🔐 Login: admin / admin123
          </div>
        </div>
        <button 
          onClick={() => window.location.reload()}
          style={{
            marginTop: '1rem',
            padding: '0.5rem 1rem',
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            borderRadius: '0.25rem',
            cursor: 'pointer',
            fontSize: '0.875rem'
          }}
        >
          Actualiser la page
        </button>
      </div>
    </div>
  );
}

export default AppSimple;
