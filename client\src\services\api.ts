import axios from 'axios';

const API_BASE_URL = 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API Services
export const authService = {
  login: async (username: string, password: string) => {
    const response = await api.post('/auth/login', { username, password });
    return response.data;
  },
  
  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data;
  }
};

export const suppliersService = {
  getAll: async () => {
    const response = await api.get('/suppliers');
    return response.data;
  },
  
  getById: async (id: number) => {
    const response = await api.get(`/suppliers/${id}`);
    return response.data;
  },
  
  create: async (supplier: any) => {
    const response = await api.post('/suppliers', supplier);
    return response.data;
  },
  
  update: async (id: number, supplier: any) => {
    const response = await api.put(`/suppliers/${id}`, supplier);
    return response.data;
  },
  
  delete: async (id: number) => {
    const response = await api.delete(`/suppliers/${id}`);
    return response.data;
  }
};

export const ordersService = {
  getAll: async () => {
    const response = await api.get('/orders');
    return response.data;
  },
  
  getById: async (id: number) => {
    const response = await api.get(`/orders/${id}`);
    return response.data;
  },
  
  create: async (order: any) => {
    const response = await api.post('/orders', order);
    return response.data;
  },
  
  update: async (id: number, order: any) => {
    const response = await api.put(`/orders/${id}`, order);
    return response.data;
  },
  
  delete: async (id: number) => {
    const response = await api.delete(`/orders/${id}`);
    return response.data;
  }
};

export const costCategoriesService = {
  getAll: async () => {
    const response = await api.get('/cost-categories');
    return response.data;
  }
};

export const dashboardService = {
  getStats: async () => {
    const response = await api.get('/dashboard/stats');
    return response.data;
  }
};

export default api;
