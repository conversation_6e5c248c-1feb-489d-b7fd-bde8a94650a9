import db from '../config/database';
import { Order, CreateOrderRequest, OrderItem, CreateOrderItemRequest, PaginatedResponse } from '../types';

export class OrderService {
  // Create a new order
  static createOrder(orderData: CreateOrderRequest, createdBy: number): Order {
    const insertOrder = db.prepare(`
      INSERT INTO orders (
        order_number, odoo_reference, supplier_id, order_date, invoice_number, invoice_date,
        from_location, to_location, bank_name, lc_number, lc_validation_date, payment_term,
        price_term, quantity_pcs, num_containers_20ft, num_containers_40ft, num_packages,
        operation_type, goods_category, currency, exchange_rate, fob_amount_currency,
        freight_amount_currency, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = insertOrder.run(
      orderData.order_number,
      orderData.odoo_reference,
      orderData.supplier_id,
      orderData.order_date,
      orderData.invoice_number,
      orderData.invoice_date,
      orderData.from_location,
      orderData.to_location,
      orderData.bank_name,
      orderData.lc_number,
      orderData.lc_validation_date,
      orderData.payment_term,
      orderData.price_term,
      orderData.quantity_pcs,
      orderData.num_containers_20ft || 0,
      orderData.num_containers_40ft || 0,
      orderData.num_packages,
      orderData.operation_type,
      orderData.goods_category,
      orderData.currency,
      orderData.exchange_rate,
      orderData.fob_amount_currency,
      orderData.freight_amount_currency,
      createdBy
    );

    return this.getOrderById(result.lastInsertRowid as number)!;
  }

  // Get order by ID
  static getOrderById(id: number): Order | null {
    const order = db.prepare(`
      SELECT o.*, s.name as supplier_name 
      FROM orders o 
      LEFT JOIN suppliers s ON o.supplier_id = s.id 
      WHERE o.id = ?
    `).get(id) as Order;
    
    return order || null;
  }

  // Get all orders with pagination
  static getAllOrders(page: number = 1, limit: number = 10, filters?: any): PaginatedResponse<Order> {
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE 1=1';
    let params: any[] = [];

    if (filters?.status) {
      whereClause += ' AND o.status = ?';
      params.push(filters.status);
    }
    if (filters?.supplier_id) {
      whereClause += ' AND o.supplier_id = ?';
      params.push(filters.supplier_id);
    }
    if (filters?.currency) {
      whereClause += ' AND o.currency = ?';
      params.push(filters.currency);
    }
    if (filters?.date_from) {
      whereClause += ' AND o.order_date >= ?';
      params.push(filters.date_from);
    }
    if (filters?.date_to) {
      whereClause += ' AND o.order_date <= ?';
      params.push(filters.date_to);
    }

    const countQuery = `SELECT COUNT(*) as total FROM orders o ${whereClause}`;
    const total = (db.prepare(countQuery).get(...params) as { total: number }).total;

    const dataQuery = `
      SELECT o.*, s.name as supplier_name 
      FROM orders o 
      LEFT JOIN suppliers s ON o.supplier_id = s.id 
      ${whereClause}
      ORDER BY o.created_at DESC 
      LIMIT ? OFFSET ?
    `;
    
    const orders = db.prepare(dataQuery).all(...params, limit, offset) as Order[];

    return {
      data: orders,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // Update order
  static updateOrder(id: number, updates: Partial<CreateOrderRequest>): Order {
    const allowedFields = [
      'odoo_reference', 'supplier_id', 'order_date', 'invoice_number', 'invoice_date',
      'from_location', 'to_location', 'bank_name', 'lc_number', 'lc_validation_date',
      'payment_term', 'price_term', 'quantity_pcs', 'num_containers_20ft', 'num_containers_40ft',
      'num_packages', 'operation_type', 'goods_category', 'currency', 'exchange_rate',
      'fob_amount_currency', 'freight_amount_currency'
    ];

    const updateFields: string[] = [];
    const values: any[] = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (allowedFields.includes(key) && value !== undefined) {
        updateFields.push(`${key} = ?`);
        values.push(value);
      }
    });

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);

    const updateQuery = `UPDATE orders SET ${updateFields.join(', ')} WHERE id = ?`;
    db.prepare(updateQuery).run(...values);

    return this.getOrderById(id)!;
  }

  // Update order status
  static updateOrderStatus(id: number, status: string): Order {
    db.prepare('UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?').run(status, id);
    return this.getOrderById(id)!;
  }

  // Delete order
  static deleteOrder(id: number): void {
    db.prepare('DELETE FROM orders WHERE id = ?').run(id);
  }

  // Get order items
  static getOrderItems(orderId: number): OrderItem[] {
    const items = db.prepare('SELECT * FROM order_items WHERE order_id = ? ORDER BY item_number').all(orderId) as OrderItem[];
    return items;
  }

  // Add order item
  static addOrderItem(itemData: CreateOrderItemRequest): OrderItem {
    const insertItem = db.prepare(`
      INSERT INTO order_items (order_id, item_number, part_number, description, quantity, unit_fob)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    const result = insertItem.run(
      itemData.order_id,
      itemData.item_number,
      itemData.part_number,
      itemData.description,
      itemData.quantity,
      itemData.unit_fob
    );

    const item = db.prepare('SELECT * FROM order_items WHERE id = ?').get(result.lastInsertRowid) as OrderItem;
    return item;
  }

  // Update order item
  static updateOrderItem(id: number, updates: Partial<CreateOrderItemRequest>): OrderItem {
    const allowedFields = ['item_number', 'part_number', 'description', 'quantity', 'unit_fob'];
    
    const updateFields: string[] = [];
    const values: any[] = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (allowedFields.includes(key) && value !== undefined) {
        updateFields.push(`${key} = ?`);
        values.push(value);
      }
    });

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    values.push(id);
    const updateQuery = `UPDATE order_items SET ${updateFields.join(', ')} WHERE id = ?`;
    db.prepare(updateQuery).run(...values);

    const item = db.prepare('SELECT * FROM order_items WHERE id = ?').get(id) as OrderItem;
    return item;
  }

  // Delete order item
  static deleteOrderItem(id: number): void {
    db.prepare('DELETE FROM order_items WHERE id = ?').run(id);
  }

  // Get order with items
  static getOrderWithItems(id: number): (Order & { items: OrderItem[] }) | null {
    const order = this.getOrderById(id);
    if (!order) return null;

    const items = this.getOrderItems(id);
    return { ...order, items };
  }

  // Get orders by supplier
  static getOrdersBySupplier(supplierId: number): Order[] {
    const orders = db.prepare(`
      SELECT o.*, s.name as supplier_name 
      FROM orders o 
      LEFT JOIN suppliers s ON o.supplier_id = s.id 
      WHERE o.supplier_id = ? 
      ORDER BY o.created_at DESC
    `).all(supplierId) as Order[];
    
    return orders;
  }

  // Get order statistics
  static getOrderStatistics(): any {
    const stats = db.prepare(`
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status IN ('DRAFT', 'APPROVED', 'ORDERED') THEN 1 END) as pending_orders,
        COUNT(CASE WHEN status = 'RECEIVED' THEN 1 END) as completed_orders,
        SUM(total_cif_dzd) as total_value,
        AVG(CASE WHEN status = 'RECEIVED' THEN 
          JULIANDAY(updated_at) - JULIANDAY(order_date) 
        END) as avg_lead_time
      FROM orders
    `).get();

    return stats;
  }
}
