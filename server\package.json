{"name": "logistics-server", "version": "1.0.0", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["logistics", "management", "typescript"], "author": "", "license": "ISC", "description": "Système de Gestion Logistique - Backend API", "dependencies": {"bcryptjs": "^3.0.2", "better-sqlite3": "^11.10.0", "cors": "^2.8.5", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "xlsx": "^0.18.5", "zod": "^3.25.43"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.13", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.15.29", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}