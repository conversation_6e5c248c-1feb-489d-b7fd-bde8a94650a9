import db from '../config/database';
import { Supplier, CreateSupplierRequest, PaginatedResponse } from '../types';

export class SupplierService {
  // Create a new supplier
  static createSupplier(supplierData: CreateSupplierRequest, createdBy: number): Supplier {
    const insertSupplier = db.prepare(`
      INSERT INTO suppliers (name, contact_email, contact_phone, address, tax_id, payment_terms, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    const result = insertSupplier.run(
      supplierData.name,
      supplierData.contact_email,
      supplierData.contact_phone,
      supplierData.address,
      supplierData.tax_id,
      supplierData.payment_terms,
      createdBy
    );

    return this.getSupplierById(result.lastInsertRowid as number)!;
  }

  // Get supplier by ID
  static getSupplierById(id: number): Supplier | null {
    const supplier = db.prepare('SELECT * FROM suppliers WHERE id = ?').get(id) as Supplier;
    return supplier || null;
  }

  // Get all suppliers with pagination
  static getAllSuppliers(page: number = 1, limit: number = 10, filters?: any): PaginatedResponse<Supplier> {
    const offset = (page - 1) * limit;
    
    let whereClause = 'WHERE 1=1';
    let params: any[] = [];

    if (filters?.is_active !== undefined) {
      whereClause += ' AND is_active = ?';
      params.push(filters.is_active);
    }
    if (filters?.search) {
      whereClause += ' AND (name LIKE ? OR contact_email LIKE ?)';
      params.push(`%${filters.search}%`, `%${filters.search}%`);
    }

    const countQuery = `SELECT COUNT(*) as total FROM suppliers ${whereClause}`;
    const total = (db.prepare(countQuery).get(...params) as { total: number }).total;

    const dataQuery = `
      SELECT * FROM suppliers 
      ${whereClause}
      ORDER BY name ASC 
      LIMIT ? OFFSET ?
    `;
    
    const suppliers = db.prepare(dataQuery).all(...params, limit, offset) as Supplier[];

    return {
      data: suppliers,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  // Get all active suppliers (for dropdowns)
  static getActiveSuppliers(): Supplier[] {
    const suppliers = db.prepare('SELECT * FROM suppliers WHERE is_active = 1 ORDER BY name ASC').all() as Supplier[];
    return suppliers;
  }

  // Update supplier
  static updateSupplier(id: number, updates: Partial<CreateSupplierRequest>): Supplier {
    const allowedFields = ['name', 'contact_email', 'contact_phone', 'address', 'tax_id', 'payment_terms'];
    
    const updateFields: string[] = [];
    const values: any[] = [];

    Object.entries(updates).forEach(([key, value]) => {
      if (allowedFields.includes(key) && value !== undefined) {
        updateFields.push(`${key} = ?`);
        values.push(value);
      }
    });

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);

    const updateQuery = `UPDATE suppliers SET ${updateFields.join(', ')} WHERE id = ?`;
    db.prepare(updateQuery).run(...values);

    return this.getSupplierById(id)!;
  }

  // Deactivate supplier (soft delete)
  static deactivateSupplier(id: number): void {
    db.prepare('UPDATE suppliers SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?').run(id);
  }

  // Activate supplier
  static activateSupplier(id: number): void {
    db.prepare('UPDATE suppliers SET is_active = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?').run(id);
  }

  // Delete supplier (hard delete - only if no orders exist)
  static deleteSupplier(id: number): void {
    // Check if supplier has orders
    const orderCount = db.prepare('SELECT COUNT(*) as count FROM orders WHERE supplier_id = ?').get(id) as { count: number };
    
    if (orderCount.count > 0) {
      throw new Error('Cannot delete supplier with existing orders. Deactivate instead.');
    }

    db.prepare('DELETE FROM suppliers WHERE id = ?').run(id);
  }

  // Get supplier statistics
  static getSupplierStatistics(id: number): any {
    const stats = db.prepare(`
      SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'RECEIVED' THEN 1 END) as completed_orders,
        SUM(total_cif_dzd) as total_value,
        AVG(total_cif_dzd) as avg_order_value,
        MIN(order_date) as first_order_date,
        MAX(order_date) as last_order_date
      FROM orders 
      WHERE supplier_id = ?
    `).get(id);

    return stats;
  }

  // Get top suppliers by order count
  static getTopSuppliers(limit: number = 10): any[] {
    const topSuppliers = db.prepare(`
      SELECT 
        s.id,
        s.name,
        COUNT(o.id) as order_count,
        SUM(o.total_cif_dzd) as total_value,
        AVG(o.total_cif_dzd) as avg_order_value
      FROM suppliers s
      LEFT JOIN orders o ON s.id = o.supplier_id
      WHERE s.is_active = 1
      GROUP BY s.id, s.name
      HAVING order_count > 0
      ORDER BY order_count DESC, total_value DESC
      LIMIT ?
    `).all(limit);

    return topSuppliers;
  }

  // Search suppliers
  static searchSuppliers(query: string): Supplier[] {
    const suppliers = db.prepare(`
      SELECT * FROM suppliers 
      WHERE is_active = 1 
      AND (name LIKE ? OR contact_email LIKE ? OR tax_id LIKE ?)
      ORDER BY name ASC
    `).all(`%${query}%`, `%${query}%`, `%${query}%`) as Supplier[];

    return suppliers;
  }
}
