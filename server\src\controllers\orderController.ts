import { Request, Response } from 'express';
import { OrderService } from '../services/orderService';
import { CreateOrderRequest, CreateOrderItemRequest } from '../types';

export class OrderController {
  // Create new order
  static async createOrder(req: Request, res: Response) {
    try {
      const orderData: CreateOrderRequest = req.body;
      const createdBy = req.user?.userId || 1;

      // Validate required fields
      if (!orderData.order_number || !orderData.supplier_id || !orderData.order_date || 
          !orderData.operation_type || !orderData.goods_category || !orderData.currency ||
          !orderData.exchange_rate || !orderData.fob_amount_currency || !orderData.freight_amount_currency) {
        return res.status(400).json({
          success: false,
          error: 'Required fields missing'
        });
      }

      const order = OrderService.createOrder(orderData, createdBy);

      res.status(201).json({
        success: true,
        data: order,
        message: 'Order created successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get all orders
  static async getAllOrders(req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const filters = {
        status: req.query.status as string,
        supplier_id: req.query.supplier_id ? parseInt(req.query.supplier_id as string) : undefined,
        currency: req.query.currency as string,
        date_from: req.query.date_from as string,
        date_to: req.query.date_to as string
      };

      const result = OrderService.getAllOrders(page, limit, filters);

      res.json({
        success: true,
        data: result
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get order by ID
  static async getOrderById(req: Request, res: Response) {
    try {
      const orderId = parseInt(req.params.id);

      if (isNaN(orderId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid order ID'
        });
      }

      const order = OrderService.getOrderById(orderId);

      if (!order) {
        return res.status(404).json({
          success: false,
          error: 'Order not found'
        });
      }

      res.json({
        success: true,
        data: order
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get order with items
  static async getOrderWithItems(req: Request, res: Response) {
    try {
      const orderId = parseInt(req.params.id);

      if (isNaN(orderId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid order ID'
        });
      }

      const order = OrderService.getOrderWithItems(orderId);

      if (!order) {
        return res.status(404).json({
          success: false,
          error: 'Order not found'
        });
      }

      res.json({
        success: true,
        data: order
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Update order
  static async updateOrder(req: Request, res: Response) {
    try {
      const orderId = parseInt(req.params.id);
      const updates = req.body;

      if (isNaN(orderId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid order ID'
        });
      }

      const order = OrderService.updateOrder(orderId, updates);

      res.json({
        success: true,
        data: order,
        message: 'Order updated successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  // Update order status
  static async updateOrderStatus(req: Request, res: Response) {
    try {
      const orderId = parseInt(req.params.id);
      const { status } = req.body;

      if (isNaN(orderId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid order ID'
        });
      }

      if (!status) {
        return res.status(400).json({
          success: false,
          error: 'Status is required'
        });
      }

      const order = OrderService.updateOrderStatus(orderId, status);

      res.json({
        success: true,
        data: order,
        message: 'Order status updated successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  // Delete order
  static async deleteOrder(req: Request, res: Response) {
    try {
      const orderId = parseInt(req.params.id);

      if (isNaN(orderId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid order ID'
        });
      }

      OrderService.deleteOrder(orderId);

      res.json({
        success: true,
        message: 'Order deleted successfully'
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get order items
  static async getOrderItems(req: Request, res: Response) {
    try {
      const orderId = parseInt(req.params.id);

      if (isNaN(orderId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid order ID'
        });
      }

      const items = OrderService.getOrderItems(orderId);

      res.json({
        success: true,
        data: items
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Add order item
  static async addOrderItem(req: Request, res: Response) {
    try {
      const itemData: CreateOrderItemRequest = req.body;

      // Validate required fields
      if (!itemData.order_id || !itemData.item_number || !itemData.part_number || 
          !itemData.description || !itemData.quantity || !itemData.unit_fob) {
        return res.status(400).json({
          success: false,
          error: 'All fields are required'
        });
      }

      const item = OrderService.addOrderItem(itemData);

      res.status(201).json({
        success: true,
        data: item,
        message: 'Order item added successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  // Update order item
  static async updateOrderItem(req: Request, res: Response) {
    try {
      const itemId = parseInt(req.params.id);
      const updates = req.body;

      if (isNaN(itemId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid item ID'
        });
      }

      const item = OrderService.updateOrderItem(itemId, updates);

      res.json({
        success: true,
        data: item,
        message: 'Order item updated successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  // Delete order item
  static async deleteOrderItem(req: Request, res: Response) {
    try {
      const itemId = parseInt(req.params.id);

      if (isNaN(itemId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid item ID'
        });
      }

      OrderService.deleteOrderItem(itemId);

      res.json({
        success: true,
        message: 'Order item deleted successfully'
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get order statistics
  static async getOrderStatistics(req: Request, res: Response) {
    try {
      const stats = OrderService.getOrderStatistics();

      res.json({
        success: true,
        data: stats
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
}
