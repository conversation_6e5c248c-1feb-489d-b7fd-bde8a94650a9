import { Request, Response } from 'express';
import { SupplierService } from '../services/supplierService';
import { CreateSupplierRequest } from '../types';

export class SupplierController {
  // Create new supplier
  static async createSupplier(req: Request, res: Response) {
    try {
      const supplierData: CreateSupplierRequest = req.body;
      const createdBy = req.user?.userId || 1;

      // Validate required fields
      if (!supplierData.name) {
        return res.status(400).json({
          success: false,
          error: 'Supplier name is required'
        });
      }

      const supplier = SupplierService.createSupplier(supplierData, createdBy);

      res.status(201).json({
        success: true,
        data: supplier,
        message: 'Supplier created successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get all suppliers
  static async getAllSuppliers(req: Request, res: Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const filters = {
        is_active: req.query.is_active !== undefined ? req.query.is_active === 'true' : undefined,
        search: req.query.search as string
      };

      const result = SupplierService.getAllSuppliers(page, limit, filters);

      res.json({
        success: true,
        data: result
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get active suppliers (for dropdowns)
  static async getActiveSuppliers(req: Request, res: Response) {
    try {
      const suppliers = SupplierService.getActiveSuppliers();

      res.json({
        success: true,
        data: suppliers
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get supplier by ID
  static async getSupplierById(req: Request, res: Response) {
    try {
      const supplierId = parseInt(req.params.id);

      if (isNaN(supplierId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid supplier ID'
        });
      }

      const supplier = SupplierService.getSupplierById(supplierId);

      if (!supplier) {
        return res.status(404).json({
          success: false,
          error: 'Supplier not found'
        });
      }

      res.json({
        success: true,
        data: supplier
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Update supplier
  static async updateSupplier(req: Request, res: Response) {
    try {
      const supplierId = parseInt(req.params.id);
      const updates = req.body;

      if (isNaN(supplierId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid supplier ID'
        });
      }

      const supplier = SupplierService.updateSupplier(supplierId, updates);

      res.json({
        success: true,
        data: supplier,
        message: 'Supplier updated successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  // Deactivate supplier
  static async deactivateSupplier(req: Request, res: Response) {
    try {
      const supplierId = parseInt(req.params.id);

      if (isNaN(supplierId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid supplier ID'
        });
      }

      SupplierService.deactivateSupplier(supplierId);

      res.json({
        success: true,
        message: 'Supplier deactivated successfully'
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Activate supplier
  static async activateSupplier(req: Request, res: Response) {
    try {
      const supplierId = parseInt(req.params.id);

      if (isNaN(supplierId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid supplier ID'
        });
      }

      SupplierService.activateSupplier(supplierId);

      res.json({
        success: true,
        message: 'Supplier activated successfully'
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Delete supplier
  static async deleteSupplier(req: Request, res: Response) {
    try {
      const supplierId = parseInt(req.params.id);

      if (isNaN(supplierId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid supplier ID'
        });
      }

      SupplierService.deleteSupplier(supplierId);

      res.json({
        success: true,
        message: 'Supplier deleted successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get supplier statistics
  static async getSupplierStatistics(req: Request, res: Response) {
    try {
      const supplierId = parseInt(req.params.id);

      if (isNaN(supplierId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid supplier ID'
        });
      }

      const stats = SupplierService.getSupplierStatistics(supplierId);

      res.json({
        success: true,
        data: stats
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get top suppliers
  static async getTopSuppliers(req: Request, res: Response) {
    try {
      const limit = parseInt(req.query.limit as string) || 10;
      const topSuppliers = SupplierService.getTopSuppliers(limit);

      res.json({
        success: true,
        data: topSuppliers
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Search suppliers
  static async searchSuppliers(req: Request, res: Response) {
    try {
      const query = req.query.q as string;

      if (!query) {
        return res.status(400).json({
          success: false,
          error: 'Search query is required'
        });
      }

      const suppliers = SupplierService.searchSuppliers(query);

      res.json({
        success: true,
        data: suppliers
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
}
