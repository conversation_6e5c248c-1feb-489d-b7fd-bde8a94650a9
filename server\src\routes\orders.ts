import { Router } from 'express';
import { OrderController } from '../controllers/orderController';
import { authenticateToken, writeAccess } from '../middleware/auth';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

// Order routes
router.get('/', OrderController.getAllOrders);
router.get('/statistics', OrderController.getOrderStatistics);
router.get('/:id', OrderController.getOrderById);
router.get('/:id/with-items', OrderController.getOrderWithItems);
router.post('/', writeAccess, OrderController.createOrder);
router.put('/:id', writeAccess, OrderController.updateOrder);
router.patch('/:id/status', writeAccess, OrderController.updateOrderStatus);
router.delete('/:id', writeAccess, OrderController.deleteOrder);

// Order items routes
router.get('/:id/items', OrderController.getOrderItems);
router.post('/items', writeAccess, OrderController.addOrderItem);
router.put('/items/:id', writeAccess, OrderController.updateOrderItem);
router.delete('/items/:id', writeAccess, OrderController.deleteOrderItem);

export default router;
