// User types
export interface User {
  id: number;
  username: string;
  password_hash: string;
  email: string;
  full_name: string;
  role: 'admin' | 'logistic' | 'purchasing' | 'viewer';
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateUserRequest {
  username: string;
  password: string;
  email: string;
  full_name: string;
  role: 'admin' | 'logistic' | 'purchasing' | 'viewer';
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: Omit<User, 'password_hash'>;
}

// Supplier types
export interface Supplier {
  id: number;
  name: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  tax_id?: string;
  payment_terms?: string;
  is_active: boolean;
  created_by: number;
  created_at: string;
  updated_at: string;
}

export interface CreateSupplierRequest {
  name: string;
  contact_email?: string;
  contact_phone?: string;
  address?: string;
  tax_id?: string;
  payment_terms?: string;
}

// Order types
export interface Order {
  id: number;
  order_number: string;
  odoo_reference?: string;
  supplier_id: number;
  order_date: string;
  invoice_number?: string;
  invoice_date?: string;
  from_location?: string;
  to_location?: string;
  bank_name?: string;
  lc_number?: string;
  lc_validation_date?: string;
  payment_term?: string;
  price_term?: string;
  quantity_pcs?: number;
  num_containers_20ft: number;
  num_containers_40ft: number;
  num_packages?: number;
  operation_type: 'Operational Expenses' | 'Investment' | 'Reinvestment' | 'Raw Materials' | 'Spare Parts' | 'Goods for Resale' | 'Services' | 'Temporary Importation';
  goods_category: 'SPARE PARTS FOR VEHICLES' | 'LUBRICANT' | 'ACCESSORY' | 'OTHER';
  currency: 'USD' | 'EURO' | 'YUAN';
  exchange_rate: number;
  fob_amount_currency: number;
  freight_amount_currency: number;
  total_cif_currency: number;
  fob_amount_dzd: number;
  freight_amount_dzd: number;
  total_cif_dzd: number;
  landed_cost_ht: number;
  landed_cost_coefficient: number;
  total_paid_ttc: number;
  status: 'DRAFT' | 'APPROVED' | 'ORDERED' | 'SHIPPED' | 'PARTIALLY_ARRIVED' | 'ARRIVED' | 'CLEARED' | 'PARTIALLY_RECEIVED' | 'RECEIVED' | 'CANCELLED';
  created_by: number;
  created_at: string;
  updated_at: string;
}

export interface CreateOrderRequest {
  order_number: string;
  odoo_reference?: string;
  supplier_id: number;
  order_date: string;
  invoice_number?: string;
  invoice_date?: string;
  from_location?: string;
  to_location?: string;
  bank_name?: string;
  lc_number?: string;
  lc_validation_date?: string;
  payment_term?: string;
  price_term?: string;
  quantity_pcs?: number;
  num_containers_20ft?: number;
  num_containers_40ft?: number;
  num_packages?: number;
  operation_type: string;
  goods_category: string;
  currency: string;
  exchange_rate: number;
  fob_amount_currency: number;
  freight_amount_currency: number;
}

// Order Item types
export interface OrderItem {
  id: number;
  order_id: number;
  item_number: number;
  part_number: string;
  description: string;
  quantity: number;
  unit_fob: number;
  amount: number;
  received_quantity: number;
}

export interface CreateOrderItemRequest {
  order_id: number;
  item_number: number;
  part_number: string;
  description: string;
  quantity: number;
  unit_fob: number;
}

// Cost Category types
export interface CostCategory {
  id: number;
  name: string;
  description?: string;
}

// Cost Detail types
export interface CostDetail {
  id: number;
  order_id: number;
  cost_category_id: number;
  sub_category?: string;
  invoice_number?: string;
  invoice_date?: string;
  total_ttc: number;
  tva: number;
  ht: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCostDetailRequest {
  order_id: number;
  cost_category_id: number;
  sub_category?: string;
  invoice_number?: string;
  invoice_date?: string;
  total_ttc: number;
  tva: number;
  notes?: string;
}

// Logistic Followup types
export interface LogisticFollowup {
  id: number;
  order_id: number;
  shipment_type: 'SEA' | 'AIR' | 'Express' | 'Other';
  voyage_number?: string;
  call_at_port?: string;
  bill_of_lading: string;
  vessel_name?: string;
  shipowner?: string;
  estimated_time_of_arrival?: string;
  actual_time_of_arrival?: string;
  customs_clearance_date?: string;
  delivery_date?: string;
  status: 'PREPARING' | 'IN_TRANSIT' | 'ARRIVED_PORT' | 'IN_CUSTOMS' | 'CLEARED_CUSTOMS' | 'EN_ROUTE_WAREHOUSE' | 'DELIVERED' | 'DELAYED' | 'CANCELLED';
  commercial_invoice_number?: string;
  packing_list_number?: string;
  certificate_of_origin?: string;
  transit_days?: number;
  customs_clearance_days?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateLogisticFollowupRequest {
  order_id: number;
  shipment_type: string;
  voyage_number?: string;
  call_at_port?: string;
  bill_of_lading: string;
  vessel_name?: string;
  shipowner?: string;
  estimated_time_of_arrival?: string;
  actual_time_of_arrival?: string;
  customs_clearance_date?: string;
  delivery_date?: string;
  status?: string;
  commercial_invoice_number?: string;
  packing_list_number?: string;
  certificate_of_origin?: string;
  notes?: string;
}

// Receipt types
export interface Receipt {
  id: number;
  order_id: number;
  receipt_number: string;
  receipt_date: string;
  notes?: string;
  created_by: number;
  created_at: string;
}

export interface CreateReceiptRequest {
  order_id: number;
  receipt_number: string;
  receipt_date: string;
  notes?: string;
}

// Receipt Item types
export interface ReceiptItem {
  id: number;
  receipt_id: number;
  order_item_id: number;
  quantity_received: number;
  unit_cost_dzd: number;
  total_cost_dzd: number;
}

export interface CreateReceiptItemRequest {
  receipt_id: number;
  order_item_id: number;
  quantity_received: number;
  unit_cost_dzd: number;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Dashboard types
export interface DashboardStats {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  totalValue: number;
  averageLeadTime: number;
  topSuppliers: Array<{
    supplier_name: string;
    order_count: number;
    total_value: number;
  }>;
}

// Excel Import types
export interface ExcelOrderData {
  orderNumber: string;
  supplierName: string;
  orderDate: string;
  items: Array<{
    itemNumber: number;
    partNumber: string;
    description: string;
    quantity: number;
    unitPrice: number;
  }>;
}
