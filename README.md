# Système de Gestion Logistique

Une application web complète pour la gestion des commandes, du suivi logistique, des coûts et de la réception des marchandises avec calcul automatique des coûts de revient.

## 🚀 Fonctionnalités Implémentées

### ✅ Architecture de Base
- **Backend Node.js** avec TypeScript et SQLite
- **Frontend React** avec TypeScript
- **Base de données SQLite** avec schéma complet
- **Authentification JWT** avec rôles utilisateurs
- **API REST** avec endpoints sécurisés

### ✅ Interface Utilisateur
- **Page de connexion** avec authentification
- **Layout principal** avec sidebar de navigation
- **Tableau de bord** avec KPI et actions rapides
- **Pages principales** : Commandes, Fournisseurs, Logistique, Coûts, Réceptions
- **Design responsive** avec CSS personnalisé

### ✅ Base de Données
- **Schéma complet** avec toutes les tables nécessaires
- **Triggers automatiques** pour les calculs de coûts
- **Colonnes générées** pour les conversions de devises
- **Contraintes d'intégrité** pour la cohérence des données

### 🔄 Fonctionnalités Métier (En Cours)
1. **Gestion des Commandes** - CRUD complet à implémenter
2. **Suivi Logistique** - Interface de tracking à développer
3. **Gestion des Coûts** - Formulaires de saisie à créer
4. **Réception des Marchandises** - Calculs automatiques à intégrer
5. **Rapports et KPI** - Tableaux de bord avancés à développer

## 🛠️ Technologies Utilisées

### Backend
- **Node.js** avec TypeScript
- **SQLite** avec better-sqlite3
- **Express.js** pour l'API REST
- **JWT** pour l'authentification
- **bcrypt** pour le hachage des mots de passe

### Frontend
- **React 18** avec TypeScript
- **Vite** pour le build et le développement
- **Tailwind CSS** pour le styling
- **React Router** pour la navigation
- **Axios** pour les appels API
- **React Query** pour la gestion des données

### Base de Données
- **SQLite** avec schéma optimisé
- **Triggers automatiques** pour les calculs de coûts
- **Colonnes générées** pour les conversions de devises
- **Contraintes d'intégrité** pour la cohérence des données

## 📦 Installation et Démarrage

### Prérequis
- Node.js 18+ 
- npm ou yarn

### Installation

1. **Cloner le projet**
```bash
git clone <repository-url>
cd YES-WEB-01-JUIN
```

2. **Installer les dépendances du backend**
```bash
cd server
npm install
```

3. **Installer les dépendances du frontend**
```bash
cd ../client
npm install
```

### Démarrage

1. **Démarrer le backend** (Terminal 1)
```bash
cd server
node basic-server.js
```
Le serveur démarre sur http://localhost:3001

2. **Démarrer le frontend** (Terminal 2)
```bash
cd client
npm run dev
```
L'application démarre sur http://localhost:5173

### Accès à l'application

- **URL**: http://localhost:5173
- **Compte par défaut**:
  - Username: `admin`
  - Password: `admin123`

## 📊 Schéma de Base de Données

### Tables Principales

1. **users** - Gestion des utilisateurs et authentification
2. **suppliers** - Base de données des fournisseurs
3. **orders** - Commandes avec calculs automatiques CIF/DZD
4. **order_items** - Articles de commande avec quantités
5. **cost_categories** - Catégories de coûts logistiques
6. **cost_details** - Détail des coûts avec calculs automatiques
7. **logistic_followups** - Suivi des expéditions
8. **receipts** - Réceptions de marchandises
9. **receipt_items** - Détail des réceptions avec coûts unitaires

### Calculs Automatiques

#### Coût de Revient (Landed Cost)
```sql
landed_cost_ht = total_cif_dzd + SUM(cost_details.ht)
landed_cost_coefficient = landed_cost_ht / total_cif_dzd
```

#### Coût Unitaire
```sql
unit_cost_dzd = unit_fob × exchange_rate × landed_cost_coefficient
```

## 🔐 Authentification et Rôles

### Rôles Utilisateurs
- **admin** - Accès complet à toutes les fonctionnalités
- **logistic** - Gestion logistique et suivi des expéditions
- **purchasing** - Gestion des commandes et fournisseurs
- **viewer** - Consultation uniquement

### Sécurité
- Authentification JWT
- Hachage des mots de passe avec bcrypt
- Middleware d'autorisation par rôle
- Protection CORS configurée

## 📱 Interface Utilisateur

### Design System
- **Tailwind CSS** pour un design moderne et responsive
- **Composants réutilisables** avec classes CSS personnalisées
- **Navigation intuitive** avec sidebar et breadcrumbs
- **Feedback utilisateur** avec notifications et états de chargement

### Pages Principales
- **Dashboard** - Vue d'ensemble avec KPI et actions rapides
- **Commandes** - Gestion complète des commandes
- **Fournisseurs** - Base de données des fournisseurs
- **Suivi Logistique** - Tracking des expéditions
- **Coûts** - Gestion des coûts logistiques
- **Réceptions** - Enregistrement des réceptions

## 🔄 API Endpoints

### Authentification
- `POST /api/auth/login` - Connexion utilisateur
- `GET /api/auth/profile` - Profil utilisateur
- `POST /api/auth/register` - Création d'utilisateur (admin)

### Commandes
- `GET /api/orders` - Liste des commandes
- `POST /api/orders` - Créer une commande
- `GET /api/orders/:id` - Détail d'une commande
- `PUT /api/orders/:id` - Modifier une commande
- `DELETE /api/orders/:id` - Supprimer une commande

### Fournisseurs
- `GET /api/suppliers` - Liste des fournisseurs
- `POST /api/suppliers` - Créer un fournisseur
- `GET /api/suppliers/:id` - Détail d'un fournisseur
- `PUT /api/suppliers/:id` - Modifier un fournisseur

## 🚧 Développement

### Structure du Projet
```
YES-WEB-01-JUIN/
├── server/                 # Backend Node.js
│   ├── src/
│   │   ├── config/        # Configuration DB
│   │   ├── controllers/   # Contrôleurs API
│   │   ├── middleware/    # Middleware Express
│   │   ├── models/        # Modèles de données
│   │   ├── routes/        # Routes API
│   │   ├── services/      # Services métier
│   │   └── types/         # Types TypeScript
│   ├── basic-server.js    # Serveur simple pour tests
│   └── package.json
├── client/                # Frontend React
│   ├── src/
│   │   ├── components/    # Composants React
│   │   ├── contexts/      # Contextes React
│   │   ├── pages/         # Pages de l'application
│   │   ├── services/      # Services API
│   │   └── types/         # Types TypeScript
│   └── package.json
└── README.md
```

### Scripts Disponibles

#### Backend
- `npm run dev` - Démarrage en mode développement
- `npm run build` - Build de production
- `npm start` - Démarrage en production

#### Frontend
- `npm run dev` - Démarrage en mode développement
- `npm run build` - Build de production
- `npm run preview` - Prévisualisation du build

## 📈 Prochaines Étapes

1. **Finaliser l'API TypeScript** - Corriger les problèmes de types
2. **Implémenter les CRUD complets** - Toutes les entités
3. **Ajouter l'import/export Excel** - Fonctionnalité métier critique
4. **Créer les rapports et KPI** - Tableaux de bord avancés
5. **Tests unitaires et d'intégration** - Qualité du code
6. **Déploiement** - Configuration production

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

---

**Développé avec ❤️ pour optimiser la gestion logistique**
