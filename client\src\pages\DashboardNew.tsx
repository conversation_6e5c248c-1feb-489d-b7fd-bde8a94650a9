import React, { useState, useEffect } from 'react';
import { dashboardService } from '../services/api';

export default function DashboardNew() {
  const [stats, setStats] = useState({
    totalOrders: 0,
    totalSuppliers: 0,
    ordersInTransit: 0,
    totalValue: 0,
    recentOrders: [],
    statusDistribution: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await dashboardService.getStats();
        if (response.success) {
          setStats(response.data);
        } else {
          setError('Failed to fetch dashboard stats');
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch dashboard stats');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'DRAFT': 'bg-gray-100 text-gray-800',
      'APPROVED': 'bg-blue-100 text-blue-800',
      'ORDERED': 'bg-yellow-100 text-yellow-800',
      'SHIPPED': 'bg-purple-100 text-purple-800',
      'PARTIALLY_ARRIVED': 'bg-orange-100 text-orange-800',
      'ARRIVED': 'bg-green-100 text-green-800',
      'CLEARED': 'bg-green-100 text-green-800',
      'PARTIALLY_RECEIVED': 'bg-yellow-100 text-yellow-800',
      'RECEIVED': 'bg-green-100 text-green-800',
      'CANCELLED': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-lg font-medium text-gray-900">Chargement...</div>
          <div className="text-sm text-gray-500">Récupération des données du tableau de bord</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-lg font-medium text-red-600">Erreur</div>
          <div className="text-sm text-gray-500">{error}</div>
        </div>
      </div>
    );
  }

  const dashboardStats = [
    { 
      name: 'Commandes totales', 
      value: stats.totalOrders.toString(), 
      change: '+12%', 
      changeType: 'positive' 
    },
    { 
      name: 'En transit', 
      value: stats.ordersInTransit.toString(), 
      change: '+2%', 
      changeType: 'positive' 
    },
    { 
      name: 'Fournisseurs actifs', 
      value: stats.totalSuppliers.toString(), 
      change: '0%', 
      changeType: 'neutral' 
    },
    { 
      name: 'Valeur totale', 
      value: formatCurrency(stats.totalValue), 
      change: '+8%', 
      changeType: 'positive' 
    },
  ];

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-semibold text-gray-900">Tableau de bord</h1>
        <p className="mt-2 text-sm text-gray-700">
          Vue d'ensemble de votre activité logistique
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {dashboardStats.map((item) => (
          <div key={item.name} className="card p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500 truncate">{item.name}</p>
                <p className="text-2xl font-semibold text-gray-900">{item.value}</p>
              </div>
              <div className={`text-sm ${
                item.changeType === 'positive' ? 'text-green-600' : 
                item.changeType === 'negative' ? 'text-red-600' : 'text-gray-500'
              }`}>
                {item.change}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Orders */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="card">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Commandes récentes</h3>
          </div>
          <div className="p-6">
            {stats.recentOrders.length > 0 ? (
              <div className="space-y-4">
                {stats.recentOrders.map((order: any) => (
                  <div key={order.id} className="flex items-center justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{order.order_number}</p>
                      <p className="text-sm text-gray-500">{order.supplier_name}</p>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {order.status}
                      </span>
                      <p className="text-sm font-medium text-gray-900">
                        {formatCurrency(order.total_cif_dzd || 0)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-sm text-gray-500">Aucune commande récente</p>
              </div>
            )}
          </div>
        </div>

        {/* Status Distribution */}
        <div className="card">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Répartition par statut</h3>
          </div>
          <div className="p-6">
            {stats.statusDistribution.length > 0 ? (
              <div className="space-y-4">
                {stats.statusDistribution.map((item: any) => (
                  <div key={item.status} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                        {item.status}
                      </span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{item.count}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-sm text-gray-500">Aucune donnée disponible</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <div className="card p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Actions rapides</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="btn-primary">
              Nouvelle commande
            </button>
            <button className="btn-outline">
              Ajouter fournisseur
            </button>
            <button className="btn-outline">
              Suivi logistique
            </button>
            <button className="btn-outline">
              Gérer les coûts
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
