import { Router } from 'express';
import { SupplierController } from '../controllers/supplierController';
import { authenticateToken, writeAccess } from '../middleware/auth';

const router = Router();

// All routes require authentication
router.use(authenticateToken);

// Supplier routes
router.get('/', SupplierController.getAllSuppliers);
router.get('/active', SupplierController.getActiveSuppliers);
router.get('/top', SupplierController.getTopSuppliers);
router.get('/search', SupplierController.searchSuppliers);
router.get('/:id', SupplierController.getSupplierById);
router.get('/:id/statistics', SupplierController.getSupplierStatistics);
router.post('/', writeAccess, SupplierController.createSupplier);
router.put('/:id', writeAccess, SupplierController.updateSupplier);
router.post('/:id/deactivate', writeAccess, SupplierController.deactivateSupplier);
router.post('/:id/activate', writeAccess, SupplierController.activateSupplier);
router.delete('/:id', writeAccess, SupplierController.deleteSupplier);

export default router;
