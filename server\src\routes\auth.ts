import { Router } from 'express';
import { AuthController } from '../controllers/authController';
import { authenticateToken, adminOnly } from '../middleware/auth';

const router = Router();

// Public routes
router.post('/login', AuthController.login);

// Protected routes
router.get('/profile', authenticateToken, AuthController.getProfile);
router.post('/change-password', authenticateToken, AuthController.changePassword);

// Admin only routes
router.post('/register', authenticateToken, adminOnly, AuthController.register);
router.get('/users', authenticateToken, adminOnly, AuthController.getAllUsers);
router.put('/users/:id', authenticateToken, adminOnly, AuthController.updateUser);
router.post('/users/:id/deactivate', authenticateToken, adminOnly, AuthController.deactivateUser);
router.post('/users/:id/activate', authenticateToken, adminOnly, AuthController.activateUser);

export default router;
