@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    margin: 0;
    min-height: 100vh;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 px-4 py-2;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 px-4 py-2;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 px-4 py-2;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .sidebar-nav {
    @apply space-y-1;
  }
  
  .sidebar-nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors;
  }
  
  .sidebar-nav-item-active {
    @apply bg-primary-100 text-primary-700;
  }
  
  .sidebar-nav-item-inactive {
    @apply text-gray-600 hover:bg-gray-50 hover:text-gray-900;
  }
}
