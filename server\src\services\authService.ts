import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import db from '../config/database';
import { User, CreateUserRequest, LoginRequest, AuthResponse } from '../types';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

export class AuthService {
  // Create a new user
  static async createUser(userData: CreateUserRequest, createdBy: number): Promise<User> {
    const { username, password, email, full_name, role } = userData;
    
    // Check if user already exists
    const existingUser = db.prepare('SELECT id FROM users WHERE username = ? OR email = ?').get(username, email);
    if (existingUser) {
      throw new Error('User with this username or email already exists');
    }

    // Hash password
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(password, saltRounds);

    // Insert user
    const insertUser = db.prepare(`
      INSERT INTO users (username, password_hash, email, full_name, role)
      VALUES (?, ?, ?, ?, ?)
    `);

    const result = insertUser.run(username, password_hash, email, full_name, role);
    
    // Get the created user
    const user = db.prepare('SELECT * FROM users WHERE id = ?').get(result.lastInsertRowid) as User;
    
    return user;
  }

  // Login user
  static async login(credentials: LoginRequest): Promise<AuthResponse> {
    const { username, password } = credentials;
    
    // Get user by username
    const user = db.prepare('SELECT * FROM users WHERE username = ? AND is_active = 1').get(username) as User;
    
    if (!user) {
      throw new Error('Invalid credentials');
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      throw new Error('Invalid credentials');
    }

    // Update last login
    db.prepare('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?').run(user.id);

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN } as jwt.SignOptions
    );

    // Return user without password hash
    const { password_hash, ...userWithoutPassword } = user;
    
    return {
      token,
      user: userWithoutPassword
    };
  }

  // Verify JWT token
  static verifyToken(token: string): any {
    try {
      return jwt.verify(token, JWT_SECRET);
    } catch (error) {
      throw new Error('Invalid token');
    }
  }

  // Get user by ID
  static getUserById(id: number): User | null {
    const user = db.prepare('SELECT * FROM users WHERE id = ? AND is_active = 1').get(id) as User;
    return user || null;
  }

  // Get all users (admin only)
  static getAllUsers(): Omit<User, 'password_hash'>[] {
    const users = db.prepare('SELECT id, username, email, full_name, role, is_active, last_login, created_at, updated_at FROM users ORDER BY created_at DESC').all() as Omit<User, 'password_hash'>[];
    return users;
  }

  // Update user
  static async updateUser(id: number, updates: Partial<CreateUserRequest>): Promise<User> {
    const { username, email, full_name, role, password } = updates;
    
    let updateFields: string[] = [];
    let values: any[] = [];

    if (username) {
      updateFields.push('username = ?');
      values.push(username);
    }
    if (email) {
      updateFields.push('email = ?');
      values.push(email);
    }
    if (full_name) {
      updateFields.push('full_name = ?');
      values.push(full_name);
    }
    if (role) {
      updateFields.push('role = ?');
      values.push(role);
    }
    if (password) {
      const password_hash = await bcrypt.hash(password, 12);
      updateFields.push('password_hash = ?');
      values.push(password_hash);
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);

    const updateQuery = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;
    db.prepare(updateQuery).run(...values);

    const updatedUser = db.prepare('SELECT * FROM users WHERE id = ?').get(id) as User;
    return updatedUser;
  }

  // Deactivate user (soft delete)
  static deactivateUser(id: number): void {
    db.prepare('UPDATE users SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?').run(id);
  }

  // Activate user
  static activateUser(id: number): void {
    db.prepare('UPDATE users SET is_active = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?').run(id);
  }

  // Create default admin user if no users exist
  static async createDefaultAdmin(): Promise<void> {
    const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number };
    
    if (userCount.count === 0) {
      const defaultAdmin: CreateUserRequest = {
        username: 'admin',
        password: 'admin123',
        email: '<EMAIL>',
        full_name: 'System Administrator',
        role: 'admin'
      };

      await this.createUser(defaultAdmin, 1);
      console.log('Default admin user created: username=admin, password=admin123');
    }
  }
}
