import Database from 'better-sqlite3';
import path from 'path';

const dbPath = path.join(__dirname, '../../database.sqlite');
const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Initialize database with schema
export const initializeDatabase = () => {
  // 1. Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      full_name TEXT NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('admin', 'logistic', 'purchasing', 'viewer')),
      is_active BOOLEAN DEFAULT 1,
      last_login TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // 2. Suppliers table
  db.exec(`
    CREATE TABLE IF NOT EXISTS suppliers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      contact_email TEXT,
      contact_phone TEXT,
      address TEXT,
      tax_id TEXT,
      payment_terms TEXT,
      is_active BOOLEAN DEFAULT 1,
      created_by INTEGER NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (created_by) REFERENCES users(id)
    );
  `);

  // 3. Orders table
  db.exec(`
    CREATE TABLE IF NOT EXISTS orders (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_number TEXT UNIQUE NOT NULL,
      odoo_reference TEXT UNIQUE,
      supplier_id INTEGER NOT NULL,
      order_date DATE NOT NULL,
      invoice_number TEXT,
      invoice_date DATE,
      from_location TEXT,
      to_location TEXT,
      bank_name TEXT,
      lc_number TEXT,
      lc_validation_date DATE,
      payment_term TEXT,
      price_term TEXT,
      quantity_pcs INTEGER,
      num_containers_20ft INTEGER DEFAULT 0,
      num_containers_40ft INTEGER DEFAULT 0,
      num_packages INTEGER,
      operation_type TEXT NOT NULL CHECK (operation_type IN (
        'Operational Expenses', 'Investment', 'Reinvestment',
        'Raw Materials', 'Spare Parts', 'Goods for Resale',
        'Services', 'Temporary Importation'
      )),
      goods_category TEXT NOT NULL CHECK (goods_category IN (
        'SPARE PARTS FOR VEHICLES', 'LUBRICANT', 'ACCESSORY', 'OTHER'
      )),
      currency TEXT NOT NULL CHECK (currency IN ('USD', 'EURO', 'YUAN')),
      exchange_rate DECIMAL(15,5) NOT NULL,
      fob_amount_currency DECIMAL(15,2) NOT NULL,
      freight_amount_currency DECIMAL(15,2) NOT NULL,
      total_cif_currency DECIMAL(15,2) GENERATED ALWAYS AS (fob_amount_currency + freight_amount_currency) STORED,
      fob_amount_dzd DECIMAL(15,2) GENERATED ALWAYS AS (fob_amount_currency * exchange_rate) STORED,
      freight_amount_dzd DECIMAL(15,2) GENERATED ALWAYS AS (freight_amount_currency * exchange_rate) STORED,
      total_cif_dzd DECIMAL(15,2) GENERATED ALWAYS AS (total_cif_currency * exchange_rate) STORED,
      landed_cost_ht DECIMAL(15,2) DEFAULT 0,
      landed_cost_coefficient DECIMAL(10,5) DEFAULT 0,
      total_paid_ttc DECIMAL(15,2) DEFAULT 0,
      status TEXT NOT NULL CHECK (status IN (
        'DRAFT', 'APPROVED', 'ORDERED', 'SHIPPED', 'PARTIALLY_ARRIVED', 'ARRIVED', 'CLEARED', 'PARTIALLY_RECEIVED', 'RECEIVED', 'CANCELLED'
      )) DEFAULT 'DRAFT',
      created_by INTEGER NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
      FOREIGN KEY (created_by) REFERENCES users(id)
    );
  `);

  // 4. Order items table
  db.exec(`
    CREATE TABLE IF NOT EXISTS order_items (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_id INTEGER NOT NULL,
      item_number INTEGER NOT NULL,
      part_number TEXT NOT NULL,
      description TEXT NOT NULL,
      quantity INTEGER NOT NULL CHECK (quantity > 0),
      unit_fob DECIMAL(15,2) NOT NULL,
      amount DECIMAL(15,2) GENERATED ALWAYS AS (quantity * unit_fob) STORED,
      received_quantity INTEGER DEFAULT 0,
      UNIQUE(order_id, item_number),
      FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
    );
  `);

  // 5. Cost categories table
  db.exec(`
    CREATE TABLE IF NOT EXISTS cost_categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      description TEXT
    );
  `);

  // 6. Cost details table
  db.exec(`
    CREATE TABLE IF NOT EXISTS cost_details (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_id INTEGER NOT NULL,
      cost_category_id INTEGER NOT NULL,
      sub_category TEXT,
      invoice_number TEXT,
      invoice_date DATE,
      total_ttc DECIMAL(15,2) NOT NULL,
      tva DECIMAL(15,2) NOT NULL,
      ht DECIMAL(15,2) GENERATED ALWAYS AS (total_ttc - tva) STORED,
      notes TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
      FOREIGN KEY (cost_category_id) REFERENCES cost_categories(id)
    );
  `);

  // 7. Logistic followups table
  db.exec(`
    CREATE TABLE IF NOT EXISTS logistic_followups (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_id INTEGER NOT NULL,
      shipment_type TEXT NOT NULL CHECK (shipment_type IN ('SEA', 'AIR', 'Express', 'Other')),
      voyage_number TEXT,
      call_at_port TEXT,
      bill_of_lading TEXT NOT NULL UNIQUE,
      vessel_name TEXT,
      shipowner TEXT,
      estimated_time_of_arrival DATETIME,
      actual_time_of_arrival DATETIME,
      customs_clearance_date DATE,
      delivery_date DATE,
      status TEXT NOT NULL CHECK (status IN (
        'PREPARING', 'IN_TRANSIT', 'ARRIVED_PORT', 'IN_CUSTOMS',
        'CLEARED_CUSTOMS', 'EN_ROUTE_WAREHOUSE', 'DELIVERED', 'DELAYED', 'CANCELLED'
      )) DEFAULT 'PREPARING',
      commercial_invoice_number TEXT,
      packing_list_number TEXT,
      certificate_of_origin TEXT,
      transit_days INTEGER,
      customs_clearance_days INTEGER,
      notes TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
    );
  `);

  // 8. Receipts table
  db.exec(`
    CREATE TABLE IF NOT EXISTS receipts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_id INTEGER NOT NULL,
      receipt_number TEXT UNIQUE NOT NULL,
      receipt_date DATE NOT NULL,
      notes TEXT,
      created_by INTEGER NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (order_id) REFERENCES orders(id),
      FOREIGN KEY (created_by) REFERENCES users(id)
    );
  `);

  // 9. Receipt items table
  db.exec(`
    CREATE TABLE IF NOT EXISTS receipt_items (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      receipt_id INTEGER NOT NULL,
      order_item_id INTEGER NOT NULL,
      quantity_received INTEGER NOT NULL CHECK (quantity_received > 0),
      unit_cost_dzd DECIMAL(15,5) NOT NULL,
      total_cost_dzd DECIMAL(15,2) GENERATED ALWAYS AS (quantity_received * unit_cost_dzd) STORED,
      FOREIGN KEY (receipt_id) REFERENCES receipts(id) ON DELETE CASCADE,
      FOREIGN KEY (order_item_id) REFERENCES order_items(id),
      UNIQUE(receipt_id, order_item_id)
    );
  `);

  // Create triggers for automatic cost calculations
  createTriggers();

  // Insert default cost categories
  insertDefaultCostCategories();

  console.log('Database initialized successfully');
};

const createTriggers = () => {
  // Trigger for updating landed cost on cost_details INSERT
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_landed_cost_on_insert
    AFTER INSERT ON cost_details
    FOR EACH ROW
    BEGIN
      UPDATE orders SET
        landed_cost_ht = (
          COALESCE((SELECT total_cif_dzd FROM orders WHERE id = NEW.order_id), 0) +
          COALESCE((SELECT SUM(ht) FROM cost_details WHERE order_id = NEW.order_id), 0)
        ),
        landed_cost_coefficient = (
          CASE
            WHEN COALESCE((SELECT total_cif_dzd FROM orders WHERE id = NEW.order_id), 0) > 0
            THEN (
              (COALESCE((SELECT total_cif_dzd FROM orders WHERE id = NEW.order_id), 0) +
               COALESCE((SELECT SUM(ht) FROM cost_details WHERE order_id = NEW.order_id), 0))
              /
              COALESCE((SELECT total_cif_dzd FROM orders WHERE id = NEW.order_id), 0)
            )
            ELSE 0
          END
        ),
        total_paid_ttc = (
          COALESCE((SELECT total_cif_dzd FROM orders WHERE id = NEW.order_id), 0) +
          COALESCE((SELECT SUM(total_ttc) FROM cost_details WHERE order_id = NEW.order_id), 0)
        ),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = NEW.order_id;
    END;
  `);

  // Trigger for updating landed cost on cost_details UPDATE
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_landed_cost_on_update
    AFTER UPDATE OF total_ttc, tva, ht ON cost_details
    FOR EACH ROW
    BEGIN
      UPDATE orders SET
        landed_cost_ht = (
          COALESCE((SELECT total_cif_dzd FROM orders WHERE id = NEW.order_id), 0) +
          COALESCE((SELECT SUM(ht) FROM cost_details WHERE order_id = NEW.order_id), 0)
        ),
        landed_cost_coefficient = (
          CASE
            WHEN COALESCE((SELECT total_cif_dzd FROM orders WHERE id = NEW.order_id), 0) > 0
            THEN (
              (COALESCE((SELECT total_cif_dzd FROM orders WHERE id = NEW.order_id), 0) +
               COALESCE((SELECT SUM(ht) FROM cost_details WHERE order_id = NEW.order_id), 0))
              /
              COALESCE((SELECT total_cif_dzd FROM orders WHERE id = NEW.order_id), 0)
            )
            ELSE 0
          END
        ),
        total_paid_ttc = (
          COALESCE((SELECT total_cif_dzd FROM orders WHERE id = NEW.order_id), 0) +
          COALESCE((SELECT SUM(total_ttc) FROM cost_details WHERE order_id = NEW.order_id), 0)
        ),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = NEW.order_id;
    END;
  `);

  // Trigger for updating landed cost on cost_details DELETE
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_landed_cost_on_delete
    AFTER DELETE ON cost_details
    FOR EACH ROW
    BEGIN
      UPDATE orders SET
        landed_cost_ht = (
          COALESCE((SELECT total_cif_dzd FROM orders WHERE id = OLD.order_id), 0) +
          COALESCE((SELECT SUM(ht) FROM cost_details WHERE order_id = OLD.order_id), 0)
        ),
        landed_cost_coefficient = (
          CASE
            WHEN COALESCE((SELECT total_cif_dzd FROM orders WHERE id = OLD.order_id), 0) > 0
            THEN (
              (COALESCE((SELECT total_cif_dzd FROM orders WHERE id = OLD.order_id), 0) +
               COALESCE((SELECT SUM(ht) FROM cost_details WHERE order_id = OLD.order_id), 0))
              /
              COALESCE((SELECT total_cif_dzd FROM orders WHERE id = OLD.order_id), 0)
            )
            ELSE 0
          END
        ),
        total_paid_ttc = (
          COALESCE((SELECT total_cif_dzd FROM orders WHERE id = OLD.order_id), 0) +
          COALESCE((SELECT SUM(total_ttc) FROM cost_details WHERE order_id = OLD.order_id), 0)
        ),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = OLD.order_id;
    END;
  `);

  // Trigger for updating received quantity on receipt_items INSERT
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_received_quantity_on_receipt_items
    AFTER INSERT ON receipt_items
    FOR EACH ROW
    BEGIN
      UPDATE order_items
      SET received_quantity = (
        SELECT SUM(quantity_received)
        FROM receipt_items
        WHERE order_item_id = NEW.order_item_id
      )
      WHERE id = NEW.order_item_id;

      -- Update order status
      UPDATE orders
      SET status = CASE
        WHEN (SELECT SUM(T2.received_quantity) FROM order_items AS T2 WHERE T2.order_id = (SELECT order_id FROM order_items WHERE id = NEW.order_item_id)) =
             (SELECT SUM(T3.quantity) FROM order_items AS T3 WHERE T3.order_id = (SELECT order_id FROM order_items WHERE id = NEW.order_item_id))
        THEN 'RECEIVED'
        WHEN (SELECT SUM(T4.received_quantity) FROM order_items AS T4 WHERE T4.order_id = (SELECT order_id FROM order_items WHERE id = NEW.order_item_id)) > 0
        THEN 'PARTIALLY_RECEIVED'
        ELSE status
      END
      WHERE id = (SELECT order_id FROM order_items WHERE id = NEW.order_item_id);
    END;
  `);
};

const insertDefaultCostCategories = () => {
  const categories = [
    { name: 'Customs Duties', description: 'Droits de douane (D3, quittances)' },
    { name: 'Port Fees', description: 'Frais portuaires (livraison, inspection)' },
    { name: 'Shipping Company Fees', description: 'Frais de compagnie maritime' },
    { name: 'Transit Services Expenses', description: 'Frais de services de transit' },
    { name: 'Other Miscellaneous Expenses', description: 'Autres dépenses diverses' }
  ];

  const insertCategory = db.prepare(`
    INSERT OR IGNORE INTO cost_categories (name, description)
    VALUES (?, ?)
  `);

  categories.forEach(category => {
    insertCategory.run(category.name, category.description);
  });
};

export default db;
