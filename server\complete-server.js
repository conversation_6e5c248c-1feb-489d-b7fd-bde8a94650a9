const express = require('express');
const cors = require('cors');
const Database = require('better-sqlite3');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const path = require('path');

const app = express();
const PORT = 3001;
const JWT_SECRET = 'your-secret-key-change-in-production';

// Database setup
const dbPath = path.join(__dirname, 'database.sqlite');
const db = new Database(dbPath);
db.pragma('foreign_keys = ON');

// Middleware
app.use(cors({
  origin: 'http://localhost:5173',
  credentials: true
}));
app.use(express.json());

// Initialize database
const initializeDatabase = () => {
  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      full_name TEXT NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('admin', 'logistic', 'purchasing', 'viewer')),
      is_active BOOLEAN DEFAULT 1,
      last_login TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `);

  // Suppliers table
  db.exec(`
    CREATE TABLE IF NOT EXISTS suppliers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      contact_email TEXT,
      contact_phone TEXT,
      address TEXT,
      tax_id TEXT,
      payment_terms TEXT,
      is_active BOOLEAN DEFAULT 1,
      created_by INTEGER NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (created_by) REFERENCES users(id)
    );
  `);

  // Orders table
  db.exec(`
    CREATE TABLE IF NOT EXISTS orders (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_number TEXT UNIQUE NOT NULL,
      odoo_reference TEXT UNIQUE,
      supplier_id INTEGER NOT NULL,
      order_date DATE NOT NULL,
      invoice_number TEXT,
      invoice_date DATE,
      from_location TEXT,
      to_location TEXT,
      bank_name TEXT,
      lc_number TEXT,
      lc_validation_date DATE,
      payment_term TEXT,
      price_term TEXT,
      quantity_pcs INTEGER,
      num_containers_20ft INTEGER DEFAULT 0,
      num_containers_40ft INTEGER DEFAULT 0,
      num_packages INTEGER,
      operation_type TEXT NOT NULL CHECK (operation_type IN (
        'Operational Expenses', 'Investment', 'Reinvestment',
        'Raw Materials', 'Spare Parts', 'Goods for Resale',
        'Services', 'Temporary Importation'
      )),
      goods_category TEXT NOT NULL CHECK (goods_category IN (
        'SPARE PARTS FOR VEHICLES', 'LUBRICANT', 'ACCESSORY', 'OTHER'
      )),
      currency TEXT NOT NULL CHECK (currency IN ('USD', 'EURO', 'YUAN')),
      exchange_rate DECIMAL(15,5) NOT NULL,
      fob_amount_currency DECIMAL(15,2) NOT NULL,
      freight_amount_currency DECIMAL(15,2) NOT NULL,
      total_cif_currency DECIMAL(15,2) GENERATED ALWAYS AS (fob_amount_currency + freight_amount_currency) STORED,
      fob_amount_dzd DECIMAL(15,2) GENERATED ALWAYS AS (fob_amount_currency * exchange_rate) STORED,
      freight_amount_dzd DECIMAL(15,2) GENERATED ALWAYS AS (freight_amount_currency * exchange_rate) STORED,
      total_cif_dzd DECIMAL(15,2) GENERATED ALWAYS AS (total_cif_currency * exchange_rate) STORED,
      landed_cost_ht DECIMAL(15,2) DEFAULT 0,
      landed_cost_coefficient DECIMAL(10,5) DEFAULT 0,
      total_paid_ttc DECIMAL(15,2) DEFAULT 0,
      status TEXT NOT NULL CHECK (status IN (
        'DRAFT', 'APPROVED', 'ORDERED', 'SHIPPED', 'PARTIALLY_ARRIVED', 'ARRIVED', 'CLEARED', 'PARTIALLY_RECEIVED', 'RECEIVED', 'CANCELLED'
      )) DEFAULT 'DRAFT',
      created_by INTEGER NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
      FOREIGN KEY (created_by) REFERENCES users(id)
    );
  `);

  // Order items table
  db.exec(`
    CREATE TABLE IF NOT EXISTS order_items (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_id INTEGER NOT NULL,
      item_number INTEGER NOT NULL,
      part_number TEXT NOT NULL,
      description TEXT NOT NULL,
      quantity INTEGER NOT NULL CHECK (quantity > 0),
      unit_fob DECIMAL(15,2) NOT NULL,
      amount DECIMAL(15,2) GENERATED ALWAYS AS (quantity * unit_fob) STORED,
      received_quantity INTEGER DEFAULT 0,
      UNIQUE(order_id, item_number),
      FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
    );
  `);

  // Cost categories table
  db.exec(`
    CREATE TABLE IF NOT EXISTS cost_categories (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      description TEXT
    );
  `);

  // Cost details table
  db.exec(`
    CREATE TABLE IF NOT EXISTS cost_details (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_id INTEGER NOT NULL,
      cost_category_id INTEGER NOT NULL,
      sub_category TEXT,
      invoice_number TEXT,
      invoice_date DATE,
      total_ttc DECIMAL(15,2) NOT NULL,
      tva DECIMAL(15,2) NOT NULL,
      ht DECIMAL(15,2) GENERATED ALWAYS AS (total_ttc - tva) STORED,
      notes TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
      FOREIGN KEY (cost_category_id) REFERENCES cost_categories(id)
    );
  `);

  // Logistic followups table
  db.exec(`
    CREATE TABLE IF NOT EXISTS logistic_followups (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_id INTEGER NOT NULL,
      shipment_type TEXT NOT NULL CHECK (shipment_type IN ('SEA', 'AIR', 'Express', 'Other')),
      voyage_number TEXT,
      call_at_port TEXT,
      bill_of_lading TEXT NOT NULL UNIQUE,
      vessel_name TEXT,
      shipowner TEXT,
      estimated_time_of_arrival DATETIME,
      actual_time_of_arrival DATETIME,
      customs_clearance_date DATE,
      delivery_date DATE,
      status TEXT NOT NULL CHECK (status IN (
        'PREPARING', 'IN_TRANSIT', 'ARRIVED_PORT', 'IN_CUSTOMS',
        'CLEARED_CUSTOMS', 'EN_ROUTE_WAREHOUSE', 'DELIVERED', 'DELAYED', 'CANCELLED'
      )) DEFAULT 'PREPARING',
      commercial_invoice_number TEXT,
      packing_list_number TEXT,
      certificate_of_origin TEXT,
      transit_days INTEGER,
      customs_clearance_days INTEGER,
      notes TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
    );
  `);

  // Receipts table
  db.exec(`
    CREATE TABLE IF NOT EXISTS receipts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      order_id INTEGER NOT NULL,
      receipt_number TEXT UNIQUE NOT NULL,
      receipt_date DATE NOT NULL,
      notes TEXT,
      created_by INTEGER NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (order_id) REFERENCES orders(id),
      FOREIGN KEY (created_by) REFERENCES users(id)
    );
  `);

  // Receipt items table
  db.exec(`
    CREATE TABLE IF NOT EXISTS receipt_items (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      receipt_id INTEGER NOT NULL,
      order_item_id INTEGER NOT NULL,
      quantity_received INTEGER NOT NULL CHECK (quantity_received > 0),
      unit_cost_dzd DECIMAL(15,5) NOT NULL,
      total_cost_dzd DECIMAL(15,2) GENERATED ALWAYS AS (quantity_received * unit_cost_dzd) STORED,
      FOREIGN KEY (receipt_id) REFERENCES receipts(id) ON DELETE CASCADE,
      FOREIGN KEY (order_item_id) REFERENCES order_items(id),
      UNIQUE(receipt_id, order_item_id)
    );
  `);

  // Create triggers and default data
  createTriggers();
  insertDefaultData();
  
  console.log('✅ Database initialized successfully');
};

const createTriggers = () => {
  // Trigger for updating landed cost on cost_details changes
  db.exec(`
    CREATE TRIGGER IF NOT EXISTS update_landed_cost_on_cost_insert
    AFTER INSERT ON cost_details
    FOR EACH ROW
    BEGIN
      UPDATE orders SET
        landed_cost_ht = (
          COALESCE(total_cif_dzd, 0) +
          COALESCE((SELECT SUM(ht) FROM cost_details WHERE order_id = NEW.order_id), 0)
        ),
        landed_cost_coefficient = (
          CASE
            WHEN COALESCE(total_cif_dzd, 0) > 0
            THEN (
              (COALESCE(total_cif_dzd, 0) +
               COALESCE((SELECT SUM(ht) FROM cost_details WHERE order_id = NEW.order_id), 0))
              / COALESCE(total_cif_dzd, 0)
            )
            ELSE 1
          END
        ),
        total_paid_ttc = (
          COALESCE(total_cif_dzd, 0) +
          COALESCE((SELECT SUM(total_ttc) FROM cost_details WHERE order_id = NEW.order_id), 0)
        ),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = NEW.order_id;
    END;
  `);
};

const insertDefaultData = () => {
  // Insert default cost categories
  const categories = [
    { name: 'Customs Duties', description: 'Droits de douane (D3, quittances)' },
    { name: 'Port Fees', description: 'Frais portuaires (livraison, inspection)' },
    { name: 'Shipping Company Fees', description: 'Frais de compagnie maritime' },
    { name: 'Transit Services Expenses', description: 'Frais de services de transit' },
    { name: 'Other Miscellaneous Expenses', description: 'Autres dépenses diverses' }
  ];

  const insertCategory = db.prepare(`
    INSERT OR IGNORE INTO cost_categories (name, description)
    VALUES (?, ?)
  `);

  categories.forEach(category => {
    insertCategory.run(category.name, category.description);
  });

  // Create default admin user
  const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
  if (userCount.count === 0) {
    const hashedPassword = bcrypt.hashSync('admin123', 10);
    db.prepare(`
      INSERT INTO users (username, password_hash, email, full_name, role)
      VALUES (?, ?, ?, ?, ?)
    `).run('admin', hashedPassword, '<EMAIL>', 'System Administrator', 'admin');
    
    console.log('✅ Default admin user created: username=admin, password=admin123');
  }
};

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Initialize database
initializeDatabase();

// Health check
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Logistics Management System API is running',
    timestamp: new Date().toISOString()
  });
});

// Authentication routes
app.post('/api/auth/login', (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password are required' });
    }

    const user = db.prepare('SELECT * FROM users WHERE username = ? AND is_active = 1').get(username);

    if (!user || !bcrypt.compareSync(password, user.password_hash)) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Update last login
    db.prepare('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?').run(user.id);

    const token = jwt.sign(
      { id: user.id, username: user.username, role: user.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    const { password_hash, ...userWithoutPassword } = user;

    res.json({
      success: true,
      token,
      user: userWithoutPassword
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/auth/profile', authenticateToken, (req, res) => {
  try {
    const user = db.prepare('SELECT id, username, email, full_name, role, is_active, last_login, created_at FROM users WHERE id = ?').get(req.user.id);
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ success: true, data: user });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ============= SUPPLIERS API =============
app.get('/api/suppliers', authenticateToken, (req, res) => {
  try {
    const suppliers = db.prepare(`
      SELECT s.*, u.full_name as created_by_name
      FROM suppliers s
      LEFT JOIN users u ON s.created_by = u.id
      WHERE s.is_active = 1
      ORDER BY s.name
    `).all();
    res.json({ success: true, data: suppliers });
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    res.status(500).json({ error: 'Failed to fetch suppliers' });
  }
});

app.post('/api/suppliers', authenticateToken, (req, res) => {
  try {
    const { name, contact_email, contact_phone, address, tax_id, payment_terms } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Name is required' });
    }

    const result = db.prepare(`
      INSERT INTO suppliers (name, contact_email, contact_phone, address, tax_id, payment_terms, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run(name, contact_email, contact_phone, address, tax_id, payment_terms, req.user.id);

    const supplier = db.prepare('SELECT * FROM suppliers WHERE id = ?').get(result.lastInsertRowid);
    res.status(201).json({ success: true, data: supplier });
  } catch (error) {
    console.error('Error creating supplier:', error);
    if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
      res.status(400).json({ error: 'Supplier name already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create supplier' });
    }
  }
});

app.put('/api/suppliers/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const { name, contact_email, contact_phone, address, tax_id, payment_terms, is_active } = req.body;

    const result = db.prepare(`
      UPDATE suppliers
      SET name = ?, contact_email = ?, contact_phone = ?, address = ?, tax_id = ?, payment_terms = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(name, contact_email, contact_phone, address, tax_id, payment_terms, is_active, id);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    const supplier = db.prepare('SELECT * FROM suppliers WHERE id = ?').get(id);
    res.json({ success: true, data: supplier });
  } catch (error) {
    console.error('Error updating supplier:', error);
    res.status(500).json({ error: 'Failed to update supplier' });
  }
});

app.delete('/api/suppliers/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;

    // Check if supplier has orders
    const orderCount = db.prepare('SELECT COUNT(*) as count FROM orders WHERE supplier_id = ?').get(id);
    if (orderCount.count > 0) {
      return res.status(400).json({ error: 'Cannot delete supplier with existing orders' });
    }

    const result = db.prepare('DELETE FROM suppliers WHERE id = ?').run(id);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Supplier not found' });
    }

    res.json({ success: true, message: 'Supplier deleted successfully' });
  } catch (error) {
    console.error('Error deleting supplier:', error);
    res.status(500).json({ error: 'Failed to delete supplier' });
  }
});

// ============= ORDERS API =============
app.get('/api/orders', authenticateToken, (req, res) => {
  try {
    const orders = db.prepare(`
      SELECT o.*, s.name as supplier_name, u.full_name as created_by_name,
             COUNT(oi.id) as items_count,
             SUM(oi.quantity) as total_quantity,
             SUM(oi.received_quantity) as total_received
      FROM orders o
      LEFT JOIN suppliers s ON o.supplier_id = s.id
      LEFT JOIN users u ON o.created_by = u.id
      LEFT JOIN order_items oi ON o.id = oi.order_id
      GROUP BY o.id
      ORDER BY o.created_at DESC
    `).all();
    res.json({ success: true, data: orders });
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({ error: 'Failed to fetch orders' });
  }
});

app.get('/api/orders/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;

    const order = db.prepare(`
      SELECT o.*, s.name as supplier_name, u.full_name as created_by_name
      FROM orders o
      LEFT JOIN suppliers s ON o.supplier_id = s.id
      LEFT JOIN users u ON o.created_by = u.id
      WHERE o.id = ?
    `).get(id);

    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const items = db.prepare(`
      SELECT oi.*,
             (oi.quantity - oi.received_quantity) as remaining_quantity,
             (oi.unit_fob * o.exchange_rate * COALESCE(o.landed_cost_coefficient, 1)) as unit_cost_dzd
      FROM order_items oi
      LEFT JOIN orders o ON oi.order_id = o.id
      WHERE oi.order_id = ?
      ORDER BY oi.item_number
    `).all(id);

    const costs = db.prepare(`
      SELECT cd.*, cc.name as category_name
      FROM cost_details cd
      LEFT JOIN cost_categories cc ON cd.cost_category_id = cc.id
      WHERE cd.order_id = ?
      ORDER BY cd.created_at
    `).all(id);

    const logistics = db.prepare(`
      SELECT * FROM logistic_followups WHERE order_id = ? ORDER BY created_at DESC
    `).all(id);

    res.json({ success: true, data: { ...order, items, costs, logistics } });
  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({ error: 'Failed to fetch order' });
  }
});

// ============= COST CATEGORIES API =============
app.get('/api/cost-categories', authenticateToken, (req, res) => {
  try {
    const categories = db.prepare('SELECT * FROM cost_categories ORDER BY name').all();
    res.json({ success: true, data: categories });
  } catch (error) {
    console.error('Error fetching cost categories:', error);
    res.status(500).json({ error: 'Failed to fetch cost categories' });
  }
});

// ============= DASHBOARD API =============
app.get('/api/dashboard/stats', authenticateToken, (req, res) => {
  try {
    const stats = {
      totalOrders: db.prepare('SELECT COUNT(*) as count FROM orders').get().count,
      totalSuppliers: db.prepare('SELECT COUNT(*) as count FROM suppliers WHERE is_active = 1').get().count,
      ordersInTransit: db.prepare("SELECT COUNT(*) as count FROM orders WHERE status IN ('SHIPPED', 'PARTIALLY_ARRIVED')").get().count,
      totalValue: db.prepare('SELECT SUM(total_cif_dzd) as total FROM orders').get().total || 0,
      recentOrders: db.prepare(`
        SELECT o.id, o.order_number, o.status, s.name as supplier_name, o.total_cif_dzd, o.created_at
        FROM orders o
        LEFT JOIN suppliers s ON o.supplier_id = s.id
        ORDER BY o.created_at DESC
        LIMIT 5
      `).all(),
      statusDistribution: db.prepare(`
        SELECT status, COUNT(*) as count
        FROM orders
        GROUP BY status
        ORDER BY count DESC
      `).all()
    };

    res.json({ success: true, data: stats });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard stats' });
  }
});

app.listen(PORT, () => {
  console.log(`🚀 Complete server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🔐 Login: username=admin, password=admin123`);
});
