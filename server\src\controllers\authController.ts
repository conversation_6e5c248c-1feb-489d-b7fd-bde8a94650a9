import { Request, Response } from 'express';
import { AuthService } from '../services/authService';
import { CreateUserRequest, LoginRequest } from '../types';

export class AuthController {
  // Register new user (admin only)
  static async register(req: Request, res: Response) {
    try {
      const userData: CreateUserRequest = req.body;
      const createdBy = req.user?.userId || 1;

      // Validate required fields
      if (!userData.username || !userData.password || !userData.email || !userData.full_name || !userData.role) {
        return res.status(400).json({
          success: false,
          error: 'All fields are required: username, password, email, full_name, role'
        });
      }

      // Validate role
      const validRoles = ['admin', 'logistic', 'purchasing', 'viewer'];
      if (!validRoles.includes(userData.role)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid role. Must be one of: admin, logistic, purchasing, viewer'
        });
      }

      const user = await AuthService.createUser(userData, createdBy);
      
      // Remove password hash from response
      const { password_hash, ...userResponse } = user;

      res.status(201).json({
        success: true,
        data: userResponse,
        message: 'User created successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  // Login user
  static async login(req: Request, res: Response) {
    try {
      const credentials: LoginRequest = req.body;

      // Validate required fields
      if (!credentials.username || !credentials.password) {
        return res.status(400).json({
          success: false,
          error: 'Username and password are required'
        });
      }

      const authResponse = await AuthService.login(credentials);

      res.json({
        success: true,
        data: authResponse,
        message: 'Login successful'
      });
    } catch (error: any) {
      res.status(401).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get current user profile
  static async getProfile(req: Request, res: Response) {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
      }

      const user = AuthService.getUserById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // Remove password hash from response
      const { password_hash, ...userResponse } = user;

      res.json({
        success: true,
        data: userResponse
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Get all users (admin only)
  static async getAllUsers(req: Request, res: Response) {
    try {
      const users = AuthService.getAllUsers();

      res.json({
        success: true,
        data: users
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Update user (admin only)
  static async updateUser(req: Request, res: Response) {
    try {
      const userId = parseInt(req.params.id);
      const updates = req.body;

      if (isNaN(userId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid user ID'
        });
      }

      const user = await AuthService.updateUser(userId, updates);
      
      // Remove password hash from response
      const { password_hash, ...userResponse } = user;

      res.json({
        success: true,
        data: userResponse,
        message: 'User updated successfully'
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  }

  // Deactivate user (admin only)
  static async deactivateUser(req: Request, res: Response) {
    try {
      const userId = parseInt(req.params.id);

      if (isNaN(userId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid user ID'
        });
      }

      // Prevent admin from deactivating themselves
      if (userId === req.user?.userId) {
        return res.status(400).json({
          success: false,
          error: 'Cannot deactivate your own account'
        });
      }

      AuthService.deactivateUser(userId);

      res.json({
        success: true,
        message: 'User deactivated successfully'
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Activate user (admin only)
  static async activateUser(req: Request, res: Response) {
    try {
      const userId = parseInt(req.params.id);

      if (isNaN(userId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid user ID'
        });
      }

      AuthService.activateUser(userId);

      res.json({
        success: true,
        message: 'User activated successfully'
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  // Change password
  static async changePassword(req: Request, res: Response) {
    try {
      const { currentPassword, newPassword } = req.body;
      const userId = req.user?.userId;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
      }

      if (!currentPassword || !newPassword) {
        return res.status(400).json({
          success: false,
          error: 'Current password and new password are required'
        });
      }

      // Get user and verify current password
      const user = AuthService.getUserById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // Verify current password by attempting login
      try {
        await AuthService.login({ username: user.username, password: currentPassword });
      } catch (error) {
        return res.status(400).json({
          success: false,
          error: 'Current password is incorrect'
        });
      }

      // Update password
      await AuthService.updateUser(userId, { password: newPassword });

      res.json({
        success: true,
        message: 'Password changed successfully'
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
}
