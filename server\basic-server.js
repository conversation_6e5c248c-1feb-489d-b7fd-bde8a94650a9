const http = require('http');
const url = require('url');

const PORT = process.env.PORT || 3001;

// Simple in-memory data for testing
const users = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    full_name: 'System Administrator',
    role: 'admin'
  }
];

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': 'http://localhost:5173',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Credentials': 'true'
};

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // Set CORS headers
  Object.keys(corsHeaders).forEach(key => {
    res.setHeader(key, corsHeaders[key]);
  });

  // Handle preflight requests
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Set content type
  res.setHeader('Content-Type', 'application/json');

  // Routes
  if (path === '/health' && method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'Logistics Management System API is running',
      timestamp: new Date().toISOString()
    }));
    return;
  }

  if (path === '/api/test' && method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'API is working!',
      data: {
        server: 'Node.js HTTP',
        database: 'SQLite (simulated)',
        timestamp: new Date().toISOString()
      }
    }));
    return;
  }

  if (path === '/api/auth/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const { username, password } = JSON.parse(body);
        
        // Simple authentication check
        if (username === 'admin' && password === 'admin123') {
          res.writeHead(200);
          res.end(JSON.stringify({
            success: true,
            data: {
              token: 'fake-jwt-token-' + Date.now(),
              user: users[0]
            },
            message: 'Login successful'
          }));
        } else {
          res.writeHead(401);
          res.end(JSON.stringify({
            success: false,
            error: 'Invalid credentials'
          }));
        }
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({
          success: false,
          error: 'Invalid JSON'
        }));
      }
    });
    return;
  }

  if (path === '/api/auth/profile' && method === 'GET') {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (token && token.startsWith('fake-jwt-token-')) {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: users[0]
      }));
    } else {
      res.writeHead(401);
      res.end(JSON.stringify({
        success: false,
        error: 'Invalid token'
      }));
    }
    return;
  }

  // 404 for all other routes
  res.writeHead(404);
  res.end(JSON.stringify({
    success: false,
    error: 'Route not found'
  }));
});

server.listen(PORT, () => {
  console.log(`🚀 Basic server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🔐 Test login: username=admin, password=admin123`);
});
