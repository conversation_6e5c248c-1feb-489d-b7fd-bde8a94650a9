import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

// Inline styles for now
const styles = {
  container: {
    height: '100vh',
    display: 'flex',
    overflow: 'hidden',
    backgroundColor: '#f3f4f6'
  },
  sidebar: {
    width: '16rem',
    backgroundColor: 'white',
    borderRight: '1px solid #e5e7eb',
    display: 'flex',
    flexDirection: 'column' as const
  },
  sidebarHeader: {
    padding: '1.25rem 1rem',
    display: 'flex',
    alignItems: 'center'
  },
  logo: {
    width: '2rem',
    height: '2rem',
    backgroundColor: '#2563eb',
    borderRadius: '0.5rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontWeight: 'bold',
    fontSize: '1.125rem'
  },
  logoText: {
    marginLeft: '0.5rem',
    fontSize: '1.25rem',
    fontWeight: '600',
    color: '#111827'
  },
  nav: {
    marginTop: '1.25rem',
    flex: 1,
    padding: '0 0.5rem',
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '0.25rem'
  },
  navItem: {
    display: 'flex',
    alignItems: 'center',
    padding: '0.5rem',
    fontSize: '0.875rem',
    fontWeight: '500',
    borderRadius: '0.375rem',
    textDecoration: 'none',
    transition: 'all 0.2s'
  },
  navItemActive: {
    backgroundColor: '#dbeafe',
    color: '#1d4ed8'
  },
  navItemInactive: {
    color: '#6b7280'
  },
  main: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column' as const,
    overflow: 'hidden'
  },
  header: {
    height: '4rem',
    backgroundColor: 'white',
    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '0 1rem'
  },
  content: {
    flex: 1,
    overflow: 'auto',
    padding: '1.5rem'
  },
  contentInner: {
    maxWidth: '80rem',
    margin: '0 auto',
    padding: '0 1rem'
  }
};

const navigation = [
  { name: 'Tableau de bord', href: '/dashboard', icon: '📊' },
  { name: 'Commandes', href: '/orders', icon: '📋' },
  { name: 'Fournisseurs', href: '/suppliers', icon: '🏢' },
  { name: 'Suivi Logistique', href: '/logistics', icon: '🚢' },
  { name: 'Coûts', href: '/costs', icon: '💰' },
  { name: 'Réceptions', href: '/receipts', icon: '📦' },
];

export default function Layout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();
  const location = useLocation();

  return (
    <div style={styles.container}>
      {/* Sidebar */}
      <div style={styles.sidebar}>
        <SidebarContent />
      </div>

      {/* Main content */}
      <div style={styles.main}>
        {/* Top bar */}
        <div style={styles.header}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>
              {user?.full_name}
            </span>
            <span style={{
              padding: '0.25rem 0.625rem',
              borderRadius: '9999px',
              fontSize: '0.75rem',
              fontWeight: '500',
              backgroundColor: '#dbeafe',
              color: '#1d4ed8'
            }}>
              {user?.role}
            </span>
            <button
              onClick={logout}
              style={{
                background: 'white',
                padding: '0.25rem',
                borderRadius: '9999px',
                color: '#9ca3af',
                border: 'none',
                cursor: 'pointer'
              }}
            >
              <svg style={{ width: '1.5rem', height: '1.5rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </button>
          </div>
        </div>

        {/* Page content */}
        <main style={styles.content}>
          <div style={styles.contentInner}>
            {children}
          </div>
        </main>
      </div>
    </div>
  );

  function SidebarContent() {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <div style={styles.sidebarHeader}>
          <div style={styles.logo}>
            <span>L</span>
          </div>
          <span style={styles.logoText}>Logistics</span>
        </div>
        <nav style={styles.nav}>
          {navigation.map((item) => {
            const isActive = location.pathname === item.href;
            return (
              <Link
                key={item.name}
                to={item.href}
                style={{
                  ...styles.navItem,
                  ...(isActive ? styles.navItemActive : styles.navItemInactive)
                }}
              >
                <span style={{ marginRight: '0.75rem', fontSize: '1.125rem' }}>{item.icon}</span>
                {item.name}
              </Link>
            );
          })}
        </nav>
      </div>
    );
  }
}
